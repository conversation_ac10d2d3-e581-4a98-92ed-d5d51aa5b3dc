<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import VueApexCharts from 'vue3-apexcharts';

const { t } = useI18n();

const seriesACOutput = ref([11.12, 88.88]); // In-house and Feed-in
const seriesLoadConsumption = ref([47.06, 52.94]); // Grid and PV + Battery

const optionsACOutput = {
  chart: {
    type: 'donut',
  },
  labels: [t('charts.in-house'), t('charts.feed-in')],
  title: {
    text: t('charts.ac-output'),
    offsetX: 45,
  },
  theme: {
    monochrome: {
      enabled: true,
      color: `rgb(${getComputedStyle(document.querySelector(':root')!).getPropertyValue('--prim-col-selected-1').replaceAll(' ', ',')})`,
      shadeTo: 'light',
      shadeIntensity: 1,
    },
  },
  dataLabels: {
    enabled: true,
    formatter(val: number) {
      return val.toFixed(2) + '%';
    },
  },
  legend: {
    position: 'bottom',
  },
};

const optionsLoadConsumption = {
  chart: {
    type: 'donut',
  },
  labels: [t('charts.grid'), t('charts.pv-bat')],
  title: {
    text: t('charts.load-consumption'),
  },
  theme: {
    monochrome: {
      enabled: true,
      color: `rgb(${getComputedStyle(document.querySelector(':root')!).getPropertyValue('--prim-col-selected-1').replaceAll(' ', ',')})`,
      shadeTo: 'light',
      shadeIntensity: 1,
    },
  },
  dataLabels: {
    enabled: true,
    formatter(val: number) {
      return val.toFixed(2) + '%';
    },
  },
  legend: {
    position: 'bottom',
  },
};
</script>

<template>
  <div class="w-full sh-full grid grid-cols-2 place-content-center justify-items-center">
    <div>
      <VueApexCharts
        width="140%"
        type="donut"
        :options="optionsACOutput"
        :series="seriesACOutput"
      />
    </div>
    <div>
      <VueApexCharts
        width="140%"
        type="donut"
        :options="optionsLoadConsumption"
        :series="seriesLoadConsumption"
      />
    </div>
  </div>
</template>
