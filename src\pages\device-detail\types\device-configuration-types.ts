export interface ConfigurationOption {
  label: string;
  value: string | number | boolean;
}

export type ConfigurationFieldType =
  | 'select'
  | 'text'
  | 'number'
  | 'switch'
  | 'checkbox'
  | 'radio';

export interface ConfigurationField {
  key: string;
  label: string;
  type: ConfigurationFieldType;
  options?: ConfigurationOption[];
  required?: boolean;
  default?: string | number | boolean;
  help?: string;
}

export type DeviceConfigurationSchema = ConfigurationField[];
