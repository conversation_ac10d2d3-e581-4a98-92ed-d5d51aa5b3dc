<script setup lang="ts">
import { computed } from 'vue';
import VChart from 'vue-echarts';
import { use } from 'echarts/core';
import {
  CanvasRenderer
} from 'echarts/renderers';
import {
  PieChart
} from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components';
import { isLightModeEnabled } from '@/composables/theme';

use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TitleComponent,
  TooltipComponent,
  LegendComponent
]);

interface GridData {
  producedEnergy: {
    value: number;
    unit: string;
  } | undefined;
  energyFromNetwork: {
    value: number;
    unit: string;
  } | undefined;
  energyToNetwork: {
    value: number;
    unit: string;
  } | undefined;
  houseConsumption: {
    value: number;
    unit: string;
  } | undefined;
}

interface Props {
  gridData: GridData;
}

const props = defineProps<Props>();

// Prvý graf: <PERSON>ozdel<PERSON><PERSON> výroby (do siete vs dom<PERSON>ca spotreba)
const productionDistributionData = computed(() => {
  if (!props.gridData.producedEnergy || !props.gridData.energyToNetwork || !props.gridData.houseConsumption) {
    return [];
  }

  const produced = props.gridData.producedEnergy.value;
  const toNetwork = props.gridData.energyToNetwork.value;
  const homeConsumptionFromPV = Math.max(0, produced - toNetwork);

  return [
    homeConsumptionFromPV, // Domáca spotreba z FV
    toNetwork // Do siete
  ];
});

const productionDistributionLabels = computed(() => [
  'Domáca spotreba',
  'Predané do siete'
]);

// Druhý graf: Pokrytie domácej spotreby (z FV vs zo siete)
const consumptionCoverageData = computed(() => {
  if (!props.gridData.houseConsumption || !props.gridData.energyFromNetwork || !props.gridData.producedEnergy || !props.gridData.energyToNetwork) {
    return [];
  }

  const totalConsumption = props.gridData.houseConsumption.value;
  const fromNetwork = props.gridData.energyFromNetwork.value;
  const fromPV = Math.max(0, totalConsumption - fromNetwork);

  return [
    fromPV, // Z FV
    fromNetwork // Zo siete
  ];
});

const consumptionCoverageLabels = computed(() => [
  'Pokryté z FV',
  'Zo siete'
]);

// Prvý graf: Rozdelenie výroby
const productionChartOptions = computed(() => {
  const unit = props.gridData.producedEnergy?.unit || 'kWh';
  const totalProduction = props.gridData.producedEnergy?.value || 0;

  return {
    darkMode: !isLightModeEnabled.value,
    title: {
      text: 'Rozdelenie výroby',
      left: 'center',
      top: 5,
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold',
        color: isLightModeEnabled.value ? '#000' : '#fff',
      },
    },
    tooltip: {
      show: true,
      trigger: 'item',
      formatter: (params: any) => {
        const percentage = params.percent;
        const value = params.value;
        return `${params.name}<br/>${Math.round(value * 1000) / 1000} ${unit} (${percentage}%)`;
      },
    },
    legend: {
      show: true,
      orient: 'horizontal',
      bottom: 0,
      textStyle: {
        color: isLightModeEnabled.value ? '#000' : '#fff',
        fontSize: 11,
      },
    },
    grid: { containLabel: true },
    series: [
      {
        name: 'Rozdelenie výroby',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        top: 10,
        itemStyle: {
          borderRadius: 0,
          borderWidth: 0,
        },
        label: {
          show: true,
          position: 'outside',
          fontSize: 11,
          color: isLightModeEnabled.value ? '#000' : '#fff',
        },
        labelLine: {
          show: true,
          showAbove: false,
          length: 10,
          length2: 15,
          smooth: false,
        },
        data: [
          {
            value: productionDistributionData.value[0],
            name: productionDistributionLabels.value[0],
            itemStyle: { color: '#f39c12' }, // Oranžová pre domácu spotrebu
            label: {
              show: true,
              position: 'outside',
              offset: [-10, 0],
              formatter: (params: any) => {
                const percentage = ((params.value / totalProduction) * 100).toFixed(1);
                return `${params.name}\n${Math.round(params.value * 1000) / 1000} ${unit}\n(${percentage}%)`;
              },
            },
          },
          {
            value: productionDistributionData.value[1],
            name: productionDistributionLabels.value[1],
            itemStyle: { color: '#27ae60' }, // Zelená pre sieť
            label: {
              show: true,
              position: 'outside',
              offset: [10, 0],
              formatter: (params: any) => {
                const percentage = ((params.value / totalProduction) * 100).toFixed(1);
                return `${params.name}\n${Math.round(params.value * 1000) / 1000} ${unit}\n(${percentage}%)`;
              },
            },
          },
        ],
      },
    ],
  };
});

const consumptionChartOptions = computed(() => {
  const unit = props.gridData.producedEnergy?.unit || 'kWh';
  const totalConsumption = props.gridData.houseConsumption?.value || 0;

  return {
    darkMode: !isLightModeEnabled.value,
    title: {
      text: 'Pokrytie spotreby',
      left: 'center',
      top: 5,
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold',
        color: isLightModeEnabled.value ? '#000' : '#fff',
      },
    },
    tooltip: {
      show: true,
      trigger: 'item',
      formatter: (params: any) => {
        const percentage = params.percent;
        const value = params.value;
        return `${params.name}<br/>${Math.round(value * 1000) / 1000} ${unit} (${percentage}%)`;
      },
    },
    legend: {
      show: true,
      orient: 'horizontal',
      bottom: 0,
      textStyle: {
        color: isLightModeEnabled.value ? '#000' : '#fff',
        fontSize: 11,
      },
    },
    series: [
      {
        name: 'Pokrytie spotreby',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        top: 10,
        itemStyle: {
          borderRadius: 0,
          borderWidth: 0,
        },
        label: {
          show: true,
          position: 'outside',
          fontSize: 11,
          color: isLightModeEnabled.value ? '#000' : '#fff',
        },
        labelLine: {
          show: true,
          showAbove: false,
          length: 10,
          length2: 15,
          smooth: false,
        },
        data: [
          {
            value: consumptionCoverageData.value[0],
            name: consumptionCoverageLabels.value[0],
            itemStyle: { color: '#27ae60' }, // Žltá pre FV
            label: {
              show: true,
              position: 'outside',
              offset: [-10, 0],
              formatter: (params: any) => {
                const percentage = ((params.value / totalConsumption) * 100).toFixed(1);
                return `${params.name}\n${Math.round(params.value * 1000) / 1000} ${unit}\n(${percentage}%)`;
              },
            },
          },
          {
            value: consumptionCoverageData.value[1],
            name: consumptionCoverageLabels.value[1],
            itemStyle: { color: '#e74c3c' }, // Červená pre sieť
            label: {
              show: true,
              position: 'outside',
              offset: [10, 0],
              formatter: (params: any) => {
                const percentage = ((params.value / totalConsumption) * 100).toFixed(1);
                return `${params.name}\n${Math.round(params.value * 1000) / 1000} ${unit}\n(${percentage}%)`;
              },
            },
          },
        ],
      },
    ],
  };
});

const hasValidData = computed(() => {
  return props.gridData?.producedEnergy &&
         props.gridData?.energyFromNetwork &&
         props.gridData?.energyToNetwork &&
         props.gridData?.houseConsumption;
});
</script>

<template>
  <div class="flex flex-col p-1 h-full">
    <div v-if="hasValidData" class="grid grid-cols-1 md:grid-cols-2 gap-4 h-full">
      <div class="flex flex-col items-center justify-center">
        <VChart
          :option="productionChartOptions"
          class="w-full"
          style="height: 192px;"
          autoresize
        />
      </div>

      <div class="flex flex-col items-center justify-center">
        <VChart
          :option="consumptionChartOptions"
          class="w-full"
          style="height: 192px;"
          autoresize
        />
      </div>
    </div>

    <div v-else class="flex flex-col items-center justify-center h-48 text-gray-500">
      <div class="text-center">
        <div class="text-lg font-medium mb-2">
          Nedostupné dáta
        </div>
        <div class="text-sm">
          Pre zobrazenie grafov sú potrebné všetky energetické údaje
        </div>
      </div>
    </div>
  </div>
</template>
