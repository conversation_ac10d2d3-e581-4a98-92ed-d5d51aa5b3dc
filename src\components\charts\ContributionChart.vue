<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import VueApexCharts from 'vue3-apexcharts';

const { t } = useI18n();

const options = {
  chart: {
    stacked: false,
  },
  theme: {
    monochrome: {
      enabled: true,
      color: `rgb(${getComputedStyle(document.querySelector(':root')!).getPropertyValue('--prim-col-selected-1').replaceAll(' ', ',')})`,
      shadeTo: 'light',
      shadeIntensity: 1,
    },
  },
  plotOptions: {
    bar: {
      borderRadius: 3,
    },
  },
  stroke: {
    curve: 'smooth',
    width: 3,
  },
  xaxis: {
    type: 'datetime',
  },
  yaxis: [
    {
      title: {
        text: 'kWh',
      },
    },
    {
      opposite: true,
      title: {
        text: t('charts.self-cons-ratio'),
      },
    },
  ],
};

const series = [
  {
    name: t('charts.pv-kwh'),
    type: 'column', // bar chart for PV(kWh)
    data: [
      { x: '2024-08-07', y: 39.9 },
      { x: '2024-08-08', y: 59.5 },
      { x: '2024-08-09', y: 51.3 },
      { x: '2024-08-10', y: 38.1 },
      { x: '2024-08-11', y: 43.1 },
      { x: '2024-08-12', y: 32.6 },
      { x: '2024-08-13', y: 42.9 },
      { x: '2024-08-14', y: 25.0 },
      { x: '2024-08-15', y: 43.9 },
      { x: '2024-08-16', y: 45.9 },
      { x: '2024-08-17', y: 46.6 },
      { x: '2024-08-18', y: 45.3 },
      { x: '2024-08-19', y: 48.1 },
      { x: '2024-08-20', y: 27.4 },
      { x: '2024-08-21', y: 51.5 },
      { x: '2024-08-22', y: 36.6 },
      { x: '2024-08-23', y: 24.4 },
      { x: '2024-08-24', y: 50.7 },
      { x: '2024-08-25', y: 32.5 },
      { x: '2024-08-26', y: 24.0 },
      { x: '2024-08-27', y: 24.9 },
      { x: '2024-08-28', y: 59.3 },
      { x: '2024-08-29', y: 30.9 },
      { x: '2024-08-30', y: 39.1 },
      { x: '2024-08-31', y: 43.6 },
      { x: '2024-09-01', y: 37.0 },
      { x: '2024-09-02', y: 42.3 },
      { x: '2024-09-03', y: 45.2 },
      { x: '2024-09-04', y: 39.0 },
      { x: '2024-09-05', y: 48.7 },
      { x: '2024-09-06', y: 55.6 },
      { x: '2024-09-07', y: 56.2 },
      { x: '2024-09-08', y: 40.4 },
      { x: '2024-09-09', y: 34.5 },
      { x: '2024-09-10', y: 47.1 },
      { x: '2024-09-11', y: 34.5 },
      { x: '2024-09-12', y: 56.1 },
      { x: '2024-09-13', y: 23.2 },
      { x: '2024-09-14', y: 21.3 },
      { x: '2024-09-15', y: 33.8 },
      { x: '2024-09-16', y: 23.7 },
      { x: '2024-09-17', y: 56.3 },
      { x: '2024-09-18', y: 22.1 },
      { x: '2024-09-19', y: 22.1 },
      { x: '2024-09-20', y: 47.8 },
      { x: '2024-09-21', y: 58.5 },
      { x: '2024-09-22', y: 56.9 },
      { x: '2024-09-23', y: 20.3 },
      { x: '2024-09-24', y: 37.9 },
      { x: '2024-09-25', y: 43.1 },
      { x: '2024-09-26', y: 32.6 },
      { x: '2024-09-27', y: 27.1 },
      { x: '2024-09-28', y: 44.0 },
      { x: '2024-09-29', y: 36.7 },
      { x: '2024-09-30', y: 43.2 },
      { x: '2024-10-01', y: 56.9 },
      { x: '2024-10-02', y: 59.3 },
      { x: '2024-10-03', y: 33.3 },
      { x: '2024-10-04', y: 30.9 },
      { x: '2024-10-05', y: 26.2 },
    ],
  },
  {
    name: t('charts.in-house-kwh'),
    type: 'column', // bar chart for In-house(kWh)
    data: [
      { x: '2024-08-07', y: 6.38 },
      { x: '2024-08-08', y: 11.38 },
      { x: '2024-08-09', y: 10.54 },
      { x: '2024-08-10', y: 12.16 },
      { x: '2024-08-11', y: 17.50 },
      { x: '2024-08-12', y: 18.81 },
      { x: '2024-08-13', y: 14.75 },
      { x: '2024-08-14', y: 6.91 },
      { x: '2024-08-15', y: 13.38 },
      { x: '2024-08-16', y: 12.20 },
      { x: '2024-08-17', y: 11.87 },
      { x: '2024-08-18', y: 18.01 },
      { x: '2024-08-19', y: 13.22 },
      { x: '2024-08-20', y: 10.53 },
      { x: '2024-08-21', y: 8.44 },
      { x: '2024-08-22', y: 8.80 },
      { x: '2024-08-23', y: 10.40 },
      { x: '2024-08-24', y: 7.78 },
      { x: '2024-08-25', y: 10.82 },
      { x: '2024-08-26', y: 16.37 },
      { x: '2024-08-27', y: 18.56 },
      { x: '2024-08-28', y: 8.81 },
      { x: '2024-08-29', y: 17.19 },
      { x: '2024-08-30', y: 18.67 },
      { x: '2024-08-31', y: 16.40 },
      { x: '2024-09-01', y: 8.73 },
      { x: '2024-09-02', y: 6.63 },
      { x: '2024-09-03', y: 6.70 },
      { x: '2024-09-04', y: 14.03 },
      { x: '2024-09-05', y: 15.98 },
      { x: '2024-09-06', y: 8.27 },
      { x: '2024-09-07', y: 11.98 },
      { x: '2024-09-08', y: 18.04 },
      { x: '2024-09-09', y: 17.38 },
      { x: '2024-09-10', y: 12.42 },
      { x: '2024-09-11', y: 7.73 },
      { x: '2024-09-12', y: 16.50 },
      { x: '2024-09-13', y: 17.63 },
      { x: '2024-09-14', y: 17.29 },
      { x: '2024-09-15', y: 10.41 },
      { x: '2024-09-16', y: 7.90 },
      { x: '2024-09-17', y: 9.96 },
      { x: '2024-09-18', y: 13.11 },
      { x: '2024-09-19', y: 10.75 },
      { x: '2024-09-20', y: 8.09 },
      { x: '2024-09-21', y: 14.45 },
      { x: '2024-09-22', y: 10.62 },
      { x: '2024-09-23', y: 6.20 },
      { x: '2024-09-24', y: 18.11 },
      { x: '2024-09-25', y: 17.84 },
      { x: '2024-09-26', y: 15.81 },
      { x: '2024-09-27', y: 13.93 },
      { x: '2024-09-28', y: 15.44 },
      { x: '2024-09-29', y: 18.27 },
      { x: '2024-09-30', y: 11.05 },
      { x: '2024-10-01', y: 7.23 },
      { x: '2024-10-02', y: 17.86 },
      { x: '2024-10-03', y: 15.72 },
      { x: '2024-10-04', y: 16.57 },
      { x: '2024-10-05', y: 14.34 },
    ],
  },
  {
    name: t('charts.sell-kwh'),
    type: 'column', // bar chart for Sell(kWh)
    data: [
      { x: '2024-08-07', y: 21.53 },
      { x: '2024-08-08', y: 39.60 },
      { x: '2024-08-09', y: 16.72 },
      { x: '2024-08-10', y: 13.53 },
      { x: '2024-08-11', y: 34.38 },
      { x: '2024-08-12', y: 30.71 },
      { x: '2024-08-13', y: 20.95 },
      { x: '2024-08-14', y: 27.90 },
      { x: '2024-08-15', y: 22.16 },
      { x: '2024-08-16', y: 28.89 },
      { x: '2024-08-17', y: 16.31 },
      { x: '2024-08-18', y: 25.93 },
      { x: '2024-08-19', y: 38.44 },
      { x: '2024-08-20', y: 11.95 },
      { x: '2024-08-21', y: 34.83 },
      { x: '2024-08-22', y: 11.26 },
      { x: '2024-08-23', y: 17.57 },
      { x: '2024-08-24', y: 17.88 },
      { x: '2024-08-25', y: 13.15 },
      { x: '2024-08-26', y: 39.27 },
      { x: '2024-08-27', y: 20.17 },
      { x: '2024-08-28', y: 24.98 },
      { x: '2024-08-29', y: 39.47 },
      { x: '2024-08-30', y: 38.58 },
      { x: '2024-08-31', y: 28.40 },
      { x: '2024-09-01', y: 33.45 },
      { x: '2024-09-02', y: 14.72 },
      { x: '2024-09-03', y: 15.25 },
      { x: '2024-09-04', y: 28.06 },
      { x: '2024-09-05', y: 24.69 },
      { x: '2024-09-06', y: 37.64 },
      { x: '2024-09-07', y: 21.64 },
      { x: '2024-09-08', y: 18.00 },
      { x: '2024-09-09', y: 20.42 },
      { x: '2024-09-10', y: 21.52 },
      { x: '2024-09-11', y: 16.62 },
      { x: '2024-09-12', y: 28.98 },
      { x: '2024-09-13', y: 36.26 },
      { x: '2024-09-14', y: 32.04 },
      { x: '2024-09-15', y: 38.71 },
      { x: '2024-09-16', y: 11.86 },
      { x: '2024-09-17', y: 20.54 },
      { x: '2024-09-18', y: 24.63 },
      { x: '2024-09-19', y: 30.87 },
      { x: '2024-09-20', y: 31.38 },
      { x: '2024-09-21', y: 27.84 },
      { x: '2024-09-22', y: 19.93 },
      { x: '2024-09-23', y: 36.67 },
      { x: '2024-09-24', y: 30.52 },
      { x: '2024-09-25', y: 13.95 },
      { x: '2024-09-26', y: 27.69 },
      { x: '2024-09-27', y: 24.17 },
      { x: '2024-09-28', y: 24.77 },
      { x: '2024-09-29', y: 23.35 },
      { x: '2024-09-30', y: 27.79 },
      { x: '2024-10-01', y: 31.64 },
      { x: '2024-10-02', y: 33.05 },
      { x: '2024-10-03', y: 22.86 },
      { x: '2024-10-04', y: 35.45 },
      { x: '2024-10-05', y: 20.45 },
    ],
  },
  {
    name: t('charts.self-cons-ratio'),
    type: 'line', // line chart for Self-Cons. Ratio(%)
    data: [
      { x: '2024-08-07', y: 15.99 },
      { x: '2024-08-08', y: 19.13 },
      { x: '2024-08-09', y: 20.55 },
      { x: '2024-08-10', y: 31.92 },
      { x: '2024-08-11', y: 40.60 },
      { x: '2024-08-12', y: 57.70 },
      { x: '2024-08-13', y: 34.38 },
      { x: '2024-08-14', y: 27.64 },
      { x: '2024-08-15', y: 30.48 },
      { x: '2024-08-16', y: 26.58 },
      { x: '2024-08-17', y: 25.47 },
      { x: '2024-08-18', y: 39.76 },
      { x: '2024-08-19', y: 27.48 },
      { x: '2024-08-20', y: 38.43 },
      { x: '2024-08-21', y: 16.39 },
      { x: '2024-08-22', y: 24.04 },
      { x: '2024-08-23', y: 42.62 },
      { x: '2024-08-24', y: 15.35 },
      { x: '2024-08-25', y: 33.29 },
      { x: '2024-08-26', y: 68.21 },
      { x: '2024-08-27', y: 74.54 },
      { x: '2024-08-28', y: 14.86 },
      { x: '2024-08-29', y: 55.63 },
      { x: '2024-08-30', y: 47.75 },
      { x: '2024-08-31', y: 37.61 },
      { x: '2024-09-01', y: 23.59 },
      { x: '2024-09-02', y: 15.67 },
      { x: '2024-09-03', y: 14.82 },
      { x: '2024-09-04', y: 35.53 },
      { x: '2024-09-05', y: 32.81 },
      { x: '2024-09-06', y: 38.83 },
      { x: '2024-09-07', y: 21.32 },
      { x: '2024-09-08', y: 44.65 },
      { x: '2024-09-09', y: 50.38 },
      { x: '2024-09-10', y: 26.37 },
      { x: '2024-09-11', y: 22.41 },
      { x: '2024-09-12', y: 29.41 },
      { x: '2024-09-13', y: 75.99 },
      { x: '2024-09-14', y: 81.17 },
      { x: '2024-09-15', y: 30.80 },
      { x: '2024-09-16', y: 33.33 },
      { x: '2024-09-17', y: 17.69 },
      { x: '2024-09-18', y: 59.32 },
      { x: '2024-09-19', y: 48.64 },
      { x: '2024-09-20', y: 16.92 },
      { x: '2024-09-21', y: 24.70 },
      { x: '2024-09-22', y: 18.66 },
      { x: '2024-09-23', y: 30.54 },
      { x: '2024-09-24', y: 47.78 },
      { x: '2024-09-25', y: 41.39 },
      { x: '2024-09-26', y: 48.50 },
      { x: '2024-09-27', y: 51.40 },
      { x: '2024-09-28', y: 35.09 },
      { x: '2024-09-29', y: 49.78 },
      { x: '2024-09-30', y: 25.58 },
      { x: '2024-10-01', y: 12.71 },
      { x: '2024-10-02', y: 30.12 },
      { x: '2024-10-03', y: 47.21 },
      { x: '2024-10-04', y: 53.62 },
      { x: '2024-10-05', y: 54.73 },
    ],
  },
];

</script>

<template>
  <div>
    <VueApexCharts
      width="100%"
      height="100%"
      :options="options"
      :series="series"
    />
  </div>
</template>
