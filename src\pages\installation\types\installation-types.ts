export type InstallationDetailData = {
  battery_capacity: number | null,
  classification: InstallationClassification | null,
  description: string | null,
  deviceInstances: DeviceInstance[],
  hasImage: boolean,
  id: string,
  is_owner: number | null,
  locality: string | null,
  title: string,
  pv_capacity: number | null,
  pv_count: number | null,
  sharedTo: {
    id: number,
    name: string,
  }[],
  hasTuyaToken: boolean,
}

export type InstallationClassification = 'on_grid' | 'hybrid'

export type DeviceType = {
  id: number,
  name: string,
}

export type DeviceVendor = {
  id: number,
  name: string,
}

export type Device = {
  id: number,
  is_approved: boolean,
  model: string,
  type?: DeviceType,
  type_id: number,
  vendor_id: number,
  vendor?: DeviceVendor,
}

export type DeviceInstance = {
  device?: Device,
  device_id: number,
  id: string,
  installation_id: string,
  serial_number: string,
  last_updated: string | null,
  configuration: Record<string, any>,
}