<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import VueApexCharts from 'vue3-apexcharts';

const { t } = useI18n();

const options = {
  chart: {
    stacked: false,
  },
  plotOptions: {
    bar: {
      borderRadius: 3,
    },
  },
  theme: {
    monochrome: {
      enabled: true,
      color: `rgb(${getComputedStyle(document.querySelector(':root')!).getPropertyValue('--prim-col-selected-1').replaceAll(' ', ',')})`,
      shadeTo: 'light',
      shadeIntensity: 1,
    },
  },
  stroke: {
    curve: 'smooth',
    width: 3,
  },
  xaxis: {
    type: 'datetime',
  },
  yaxis: [
    {
      title: {
        text: 'kWh',
      },
    },
    {
      opposite: true,
      title: {
        text: t('charts.self-cons-ratio'),
      },
    },
  ],
};

const series = [
  {
    name: t('charts.pv-kwh'),
    type: 'column', // bar chart for PV(kWh)
    data: [
      { x: '2024-08-07', y: 42.3 },
      { x: '2024-08-08', y: 42.9 },
      { x: '2024-08-09', y: 41.2 },
      { x: '2024-08-10', y: 57.6 },
      { x: '2024-08-11', y: 26.2 },
      // ... add the remaining data points here
    ],
  },
  {
    name: t('charts.in-house-kwh'),
    type: 'column', // bar chart for In-house(kWh)
    data: [
      { x: '2024-08-07', y: 8.69 },
      { x: '2024-08-08', y: 12.90 },
      { x: '2024-08-09', y: 10.51 },
      { x: '2024-08-10', y: 9.88 },
      { x: '2024-08-11', y: 5.96 },
      // ... add the remaining data points here
    ],
  },
  {
    name: t('charts.sell-kwh'),
    type: 'column', // bar chart for Sell(kWh)
    data: [
      { x: '2024-08-07', y: 14.82 },
      { x: '2024-08-08', y: 35.84 },
      { x: '2024-08-09', y: 33.94 },
      { x: '2024-08-10', y: 24.31 },
      { x: '2024-08-11', y: 35.13 },
      // ... add the remaining data points here
    ],
  },
  {
    name: t('charts.self-cons-ratio'),
    type: 'line', // line chart for Self-Cons. Ratio(%)
    data: [
      { x: '2024-08-07', y: 20.54 },
      { x: '2024-08-08', y: 30.07 },
      { x: '2024-08-09', y: 25.51 },
      { x: '2024-08-10', y: 17.15 },
      { x: '2024-08-11', y: 22.75 },
      // ... add the remaining data points here
    ],
  },
];

</script>

<template>
  <div>
    <VueApexCharts
      width="100%"
      height="100%"
      :options="options"
      :series="series"
    />
  </div>
</template>
