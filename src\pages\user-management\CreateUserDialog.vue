<script setup lang="ts">
import { reactive } from 'vue';
import { useI18n } from 'vue-i18n';
import { Button } from '@/shadcn-components/ui/button';
import { Input } from '@/shadcn-components/ui/input';
import { Label } from '@/shadcn-components/ui/label';
import { customAxios } from '@/util/axios';
import { deployToast, ToastType } from '@/util/toast';
import type { RoleData } from '@/util/types/roles-and-permissions';

interface Props {
  allRoles: RoleData[]
}

const isOpened = defineModel<boolean>();
const { t } = useI18n();
const newUser = reactive({
  mail: '',
  name: '',
  roles: [],
});

defineProps<Props>();

const emit = defineEmits(['userCreated']);

const onSubmit = async() => {
  try {
    await customAxios.post('/users', {
      name: newUser.name,
      email: newUser.mail,
      roles: newUser.roles,
    });
    newUser.mail = '';
    newUser.name = '';
    newUser.roles = [];
    isOpened.value = false;
    emit('userCreated');
    deployToast(ToastType.SUCCESS, {
      text: t('user-management.user-create-success'),
      timeout: 6000,
    });
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('user-management.user-create-fail'),
      timeout: 6000,
    });
  }
};
</script>

<template>
  <Teleport to="body">
    <v-dialog
      v-model="isOpened"
      width="fit-content"
      :style="{margin: 'auto'}"
    >
      <form
        autocomplete="off"
        class="bg-prim-col-1 p-6 rounded-2xl w-[35rem] max-w-[90vw]"
        @submit.prevent="onSubmit"
      >
        <h3 class="font-bold text-xl mb-2">
          {{ $t('user-management.create-user') }}
        </h3>
        <p class="text-black/50 dark:text-white/60 text-sm">
          {{ $t('user-management.create-user-desc') }}
        </p>
        <div class="grid gap-4 py-4">
          <div class="flex items-center gap-4">
            <Label
              for="new-user-name"
              class="text-right w-16"
            >
              {{ $t('misc.name') }}
            </Label>
            <Input
              id="new-user-name"
              v-model="newUser.name"
              type="text"
              required
            />
          </div>
          <div class="flex items-center gap-4">
            <Label
              for="new-user-mail"
              class="text-right w-16"
            >
              {{ $t('misc.mail') }}
            </Label>
            <Input
              id="new-user-mail"
              v-model="newUser.mail"
              type="email"
              required
            />
          </div>
          <div class="flex items-center gap-4">
            <Label
              for="new-user-roles"
              class="text-right w-16"
            >
              {{ $t('user-management.roles') }}
            </Label>
            <v-select
              id="new-user-roles"
              v-model="newUser.roles"
              label="Select"
              :hide-details="true"
              :single-line="true"
              :items="allRoles"
              item-title="name"
              item-value="id"
              multiple
              variant="solo-filled"
              width="100%"
            >
              <template #selection="{ item, index }">
                <v-chip v-if="index < 1">
                  <span class="text-xs">{{ item.title }}</span>
                </v-chip>
                <span
                  v-if="index === 1"
                  class="text-xs text-grey text-caption align-self-center"
                >
                  (+{{ newUser.roles.length - 1 }} others)
                </span>
              </template>
            </v-select>
          </div>
        </div>
        <div class="flex items-center justify-end gap-2">
          <Button
            type="button"
            variant="default"
            class="bg-gray-400/80 hover:bg-gray-400"
            @click="isOpened = false"
          >
            <span>{{ $t('misc.cancel') }}</span>
          </Button>
          <Button type="submit">
            {{ $t('misc.save') }}
          </Button>
        </div>
      </form>
    </v-dialog>
  </Teleport>
</template>
