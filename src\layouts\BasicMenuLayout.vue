<script lang="ts" setup>
import NavBar from '@/components/global/NavBar.vue';
import SideBar from '@/components/global/SideBar.vue';
import { useAuthStore } from '@/stores/auth-store';

const authStore = useAuthStore();
</script>

<template>
  <template v-if="authStore.user">
    <transition
      appear
      :duration="200"
    >
      <div class="min-h-[100dvh] flex w-full flex-col bg-prim-col-1">
        <side-bar />
        <div class="flex flex-col sm:gap-4 sm:pl-14 pb-4">
          <nav-bar />
          <main class="flex-1 p-4 sm:px-6 sm:py-0">
            <slot />
          </main>
        </div>
      </div>
    </transition>
  </template>
  <template v-else>
    <slot />
  </template>
</template>
