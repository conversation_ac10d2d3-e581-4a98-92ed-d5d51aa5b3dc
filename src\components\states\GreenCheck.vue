<script lang="ts" setup>
</script>

<template>
  <div class="rounded-full flex items-center justify-center animate-check bg-green-500 overflow-hidden will-change-transform">
    <font-awesome-icon
      class="fa-icon transition-all duration-100 h-3/6 text-white"
      icon="check"
    />
  </div>
</template>

<style scoped>

@keyframes animate-check-fgpw {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes animate-check-icon-fgpw {
  0% {
    opacity: 0;
    transform: translateX(-25%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-check {
  opacity: 0;
  animation: animate-check-fgpw forwards 1s ease-out;
  animation-delay: 0.25s;
  .fa-icon {
    opacity: 0;
    transform: translateX(-10%);
    animation: animate-check-icon-fgpw forwards 0.5s ease-out;
    animation-delay: 0.55s;
  }
}

</style>
