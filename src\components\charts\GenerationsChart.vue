<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import VueApexCharts from 'vue3-apexcharts';

const { t } = useI18n();

const options = {
  chart: {
    id: 'generation',
    stacked: false, // allows different types of charts
  },
  plotOptions: {
    bar: {
      borderRadius: 3,
    },
  },
  theme: {
    monochrome: {
      enabled: true,
      color: `rgb(${getComputedStyle(document.querySelector(':root')!).getPropertyValue('--prim-col-selected-1').replaceAll(' ', ',')})`,
      shadeTo: 'light',
      shadeIntensity: 1,
    },
  },
  stroke: {
    curve: 'smooth',
    width: 3,
  },
  xaxis: {
    type: 'datetime', // use datetime for time-based data
  },
  yaxis: [
    {
      title: {
        text: t('charts.pv-kwh'), // Y-axis title for PV(kWh)
      },
    },
    {
      opposite: true, // Place the second y-axis on the right
      title: {
        text: t('charts.income-eur'), // Y-axis title for Income
      },
    },
  ],
};

const series = [
  {
    name: t('charts.pv-kwh'),
    type: 'column',
    data: [
      { x: '2024-08-07', y: 42.1 },
      { x: '2024-08-08', y: 42.5 },
      { x: '2024-08-09', y: 46.1 },
      { x: '2024-08-10', y: 23.4 },
      { x: '2024-08-11', y: 45.6 },
      { x: '2024-08-12', y: 58.2 },
      { x: '2024-08-13', y: 42.2 },
      { x: '2024-08-14', y: 45.1 },
      { x: '2024-08-15', y: 20.8 },
      { x: '2024-08-16', y: 32.8 },
      { x: '2024-08-17', y: 57.5 },
      { x: '2024-08-18', y: 44.0 },
      { x: '2024-08-19', y: 42.7 },
      { x: '2024-08-20', y: 51.6 },
      { x: '2024-08-21', y: 34.1 },
      { x: '2024-08-22', y: 26.9 },
      { x: '2024-08-23', y: 49.1 },
      { x: '2024-08-24', y: 44.2 },
      { x: '2024-08-25', y: 52.4 },
      { x: '2024-08-26', y: 48.9 },
      { x: '2024-08-27', y: 41.7 },
      { x: '2024-08-28', y: 45.3 },
      { x: '2024-08-29', y: 38.5 },
      { x: '2024-08-30', y: 43.9 },
      { x: '2024-08-31', y: 22.3 },
      { x: '2024-09-01', y: 46.1 },
      { x: '2024-09-02', y: 23.2 },
      { x: '2024-09-03', y: 57.7 },
      { x: '2024-09-04', y: 28.8 },
      { x: '2024-09-05', y: 55.5 },
    ],
  },
  {
    name: t('charts.income-eur'),
    type: 'line',
    data: [
      { x: '2024-08-07', y: 7.23 },
      { x: '2024-08-08', y: 9.83 },
      { x: '2024-08-09', y: 11.81 },
      { x: '2024-08-10', y: 7.23 },
      { x: '2024-08-11', y: 9.83 },
      { x: '2024-08-12', y: 11.27 },
      { x: '2024-08-13', y: 8.65 },
      { x: '2024-08-14', y: 8.26 },
      { x: '2024-08-15', y: 8.81 },
      { x: '2024-08-16', y: 7.25 },
      { x: '2024-08-17', y: 10.25 },
      { x: '2024-08-18', y: 9.01 },
      { x: '2024-08-19', y: 9.57 },
      { x: '2024-08-20', y: 7.21 },
      { x: '2024-08-21', y: 8.93 },
      { x: '2024-08-22', y: 11.53 },
      { x: '2024-08-23', y: 7.5 },
      { x: '2024-08-24', y: 6.74 },
      { x: '2024-08-25', y: 10.61 },
      { x: '2024-08-26', y: 5.25 },
      { x: '2024-08-27', y: 10.51 },
      { x: '2024-08-28', y: 7.06 },
      { x: '2024-08-29', y: 7.68 },
      { x: '2024-08-30', y: 10.84 },
      { x: '2024-08-31', y: 9.24 },
      { x: '2024-09-01', y: 5.11 },
      { x: '2024-09-02', y: 8.28 },
      { x: '2024-09-03', y: 8.09 },
      { x: '2024-09-04', y: 11.6 },
      { x: '2024-09-05', y: 5.97 },
    ],
  },
];
</script>

<template>
  <div>
    <VueApexCharts
      width="100%"
      height="100%"
      :options="options"
      :series="series"
    />
  </div>
</template>
