<script lang="ts" setup>
import isEqual from 'lodash-es/isEqual';
import { Trash2, Check, X } from 'lucide-vue-next';
import { nextTick, reactive, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/shadcn-components/ui/alert-dialog';
import { Button } from '@/shadcn-components/ui/button';
import { Input } from '@/shadcn-components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shadcn-components/ui/table';
import { customAxios } from '@/util/axios';
import { deployToast, ToastType } from '@/util/toast';
import type { PermissionData, RoleData } from '@/util/types/roles-and-permissions';

interface Props {
  permissions: PermissionData[],
  creatingNew: boolean,
  formerRoleData: RoleData[],
}

const props = defineProps<Props>();
const emit = defineEmits(['setCreatingNew', 'newRoleCreated', 'roleDeleted', 'roleUpdated']);
const { t } = useI18n();

const newItem = reactive<Pick<RoleData, 'name' | 'permissions'>>({
  name: '',
  permissions: [],
});

const rolesDataChanges = defineModel<RoleData[]>({ required: true });

const resetNewItem = () => {
  emit('setCreatingNew', false);
  newItem.name = '';
  newItem.permissions = [];
};

const saveEdits = async(updatedRole: RoleData) => {
  try {
    await customAxios.put(`/roles/${updatedRole.id}`, updatedRole);
    emit('roleUpdated');
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('role-management.edit-roles-failed'),
      timeout: 6000,
    });
  }
};

const createNewRole = async() => {
  try {
    await customAxios.post('/roles', newItem);
    emit('newRoleCreated');
    resetNewItem();
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('role-management.failed-create-role'),
      timeout: 6000,
    });
  }
};

const removeRole = async(roleToDelete: RoleData) => {
  try {
    await customAxios.delete(`/roles/${roleToDelete.id}`);
    emit('roleDeleted');
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('role-management.failed-delete-role'),
      timeout: 6000,
    });
  }
};

const cancelEdits = (role: RoleData, originalIdx: number) => {
  role.name = props.formerRoleData[originalIdx].name;
  role.permissions = props.formerRoleData[originalIdx].permissions;
};

watch(() => props.creatingNew, async newValue => {
  if (newValue) {
    await nextTick();
    (document.querySelector('input#input-add-role') as HTMLInputElement)?.focus();
  }
});
</script>

<template>
  <Table
    v-if="rolesDataChanges.length > 0"
    class="users-table"
  >
    <TableHeader>
      <TableRow>
        <TableHead>
          {{ $t('role-management.role-name') }}
        </TableHead>
        <TableHead>
          {{ $t('user-management.permissions') }}
        </TableHead>
        <TableHead class="text-right">
          {{ $t('user-management.actions') }}
        </TableHead>
      </TableRow>
    </TableHeader>
    <TableBody>
      <TableRow
        v-if="creatingNew"
        class="bg-yellow-400/15 hover:bg-yellow-400/20"
      >
        <TableCell>
          <Input
            id="input-add-role"
            v-model="newItem.name"
            type="text"
            required
            class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500 max-w-[240px]"
          />
        </TableCell>
        <TableCell>
          <div>
            <v-select
              v-model="newItem.permissions"
              label="Select"
              :hide-details="true"
              :single-line="true"
              :items="permissions.map(v => v.name)"
              multiple
              variant="solo-filled"
              width="fit-content"
              min-width="240px"
            >
              <template #selection="{ item, index }">
                <v-chip v-if="index < 1">
                  <span class="text-xs">{{ item.title }}</span>
                </v-chip>
                <span
                  v-if="index === 1"
                  class="text-xs text-grey text-caption align-self-center"
                >
                  (+{{ permissions.length - 1 }} others)
                </span>
              </template>
            </v-select>
          </div>
        </TableCell>
        <TableCell class="text-right">
          <div class="flex justify-end items-center flex-nowrap gap-1.5">
            <Button
              class="w-8 h-8 p-1.5 rounded-full bg-green-500 hover:bg-green-600"
              variant="default"
              @click="createNewRole"
            >
              <Check class="w-full h-full text-white" />
            </Button>
            <Button
              class="w-8 h-8 p-1.5 rounded-full "
              variant="destructive"
              @click="resetNewItem"
            >
              <X class="w-full h-full" />
            </Button>
          </div>
        </TableCell>
      </TableRow>
      <TableRow
        v-for="(role, idx) in [...rolesDataChanges]"
        :key="idx"
        :class="[isEqual(role, formerRoleData[idx]) ? (idx % 2 === 0 ? 'bg-accent/5 dark:bg-accent' : '') : 'bg-yellow-400/15 hover:bg-yellow-400/20']"
        class=""
      >
        <TableCell>
          <Input
            v-model="role.name"
            type="text"
            required
            class="min-w-[7rem] max-w-[240px] ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 bg-white dark:bg-background border-prim-col-1 focus-visible:border-gray-300 dark:focus-visible:border-gray-500"
          />
        </TableCell>
        <TableCell>
          <div>
            <v-select
              v-model="role.permissions"
              label="Select"
              :hide-details="true"
              :single-line="true"
              :items="permissions.map(v => v.name)"
              multiple
              variant="solo-filled"
              width="fit-content"
              min-width="240px"
            >
              <template #selection="{ item, index }">
                <v-chip v-if="index < 1">
                  <span class="text-xs">{{ item.title }}</span>
                </v-chip>
                <span
                  v-if="index === 1"
                  class="text-xs text-grey text-caption align-self-center"
                >
                  (+{{ role.permissions?.length - 1 }} others)
                </span>
              </template>
            </v-select>
          </div>
        </TableCell>
        <TableCell class="text-right w-36">
          <div
            v-if="!isEqual(role, formerRoleData[idx])"
            class="inline-flex items-center gap-1"
          >
            <Button
              class="w-8 h-8 p-1.5 rounded-full bg-green-500 hover:bg-green-600"
              variant="default"
              @click="saveEdits(role)"
            >
              <Check class="w-full h-full text-white" />
            </Button>
            <Button
              class="w-8 h-8 p-1.5 rounded-full bg-prim-col-foreground-2 hover:bg-prim-col-foreground-2/70"
              variant="default"
              @click="cancelEdits(role, idx)"
            >
              <X class="w-full h-full text-white" />
            </Button>
          </div>
          <AlertDialog v-if="isEqual(role, formerRoleData[idx])">
            <AlertDialogTrigger>
              <Button
                class="w-8 h-8 p-1.5 rounded-full"
                variant="destructive"
              >
                <Trash2 class="w-full h-full" />
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>{{ $t('role-management.delete-role-action', {name: role.name}) }}</AlertDialogTitle>
                <AlertDialogDescription>{{ $t('user.delete-user.modal-desc') }}</AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>{{ $t('misc.cancel') }}</AlertDialogCancel>
                <AlertDialogAction
                  class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  @click="removeRole(role)"
                >
                  {{ $t('misc.continue') }}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </TableCell>
      </TableRow>
    </TableBody>
  </Table>
</template>
