<script setup lang="ts">
import { ref, computed } from 'vue';
import type { DeviceInstance } from '@/pages/installation/types/installation-types';
import type { DeviceConfigurationSchema, ConfigurationField } from '../types/device-configuration-types';
import { customAxios } from '@/util/axios';

interface Props {
  deviceDetail: DeviceInstance;
  installationId: string;
}

const props = defineProps<Props>();

const configSchema = ref<DeviceConfigurationSchema>();
const deviceConfig = ref<Record<string, any>>({ ...props.deviceDetail.configuration });
const notAvailable = ref(false);

try {
  const schemaResponse = await customAxios.get<DeviceConfigurationSchema>(
    `/user/device/configuration-schema/${props.installationId}/${props.deviceDetail.id}`
  );
  configSchema.value = schemaResponse.data;
} catch {
  notAvailable.value = true;
}

const fields = computed<ConfigurationField[]>(() => configSchema.value ?? []);

const saveConfig = () => {
  console.log('Save configuration:', deviceConfig.value);
};
</script>

<template>
  <div v-if="notAvailable" class="p-4 text-gray-500">
    Configuration not available for this vendor.
  </div>

  <form v-else class="space-y-4" @submit.prevent="saveConfig">
    <div
      v-for="field in fields"
      :key="field.key"
      class="flex flex-col space-y-1 pb-3"
    >
      <label :for="field.key" class="font-medium text-sm">
        {{ field.label }}
        <span v-if="field.required" class="text-red-500">*</span>
      </label>

      <select
        v-if="field.type === 'select'"
        :id="field.key"
        v-model="deviceConfig[field.key]"
        class="border rounded px-2 py-1"
      >
        <option disabled value="">
          -- Select --
        </option>
        <option
          v-for="(opt, idx) in field.options"
          :key="idx"
          :value="opt.value"
        >
          {{ opt.label }}
        </option>
      </select>

      <input
        v-else-if="field.type === 'text' || field.type === 'number'"
        :id="field.key"
        v-model="deviceConfig[field.key]"
        :type="field.type"
        class="border rounded px-2 py-1"
      >

      <input
        v-else-if="field.type === 'switch' || field.type === 'checkbox'"
        :id="field.key"
        v-model="deviceConfig[field.key]"
        type="checkbox"
      >

      <div v-else class="text-xs text-gray-400">
        Unknown field type: {{ field.type }}
      </div>

      <small v-if="field.help" class="text-gray-500">{{ field.help }}</small>
    </div>

    <div class="pt-4">
      <button
        type="submit"
        class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
      >
        Save
      </button>
    </div>
  </form>
</template>

<style scoped>
select,
input[type='text'],
input[type='number'] {
  width: 100%;
}
</style>
