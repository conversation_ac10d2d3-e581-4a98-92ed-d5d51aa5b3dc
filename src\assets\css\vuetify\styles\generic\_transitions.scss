@use '../settings';

@mixin transition-default() {
  &-enter-active {
    transition-duration: 0.3s;
    transition-timing-function: settings.$standard-easing;
  }

  &-leave-active {
    transition-duration: 0.3s;
    transition-timing-function: settings.$standard-easing;
  }

  &-move {
    transition-duration: 0.5s;
    transition-property: transform;
    transition-timing-function: settings.$standard-easing;
  }
}
@mixin fade-out() {
  &-leave-to {
    opacity: 0;
  }
  &-leave-active {
    transition-duration: 100ms;
  }
}

// Component specific transitions
.dialog-transition,
.dialog-bottom-transition,
.dialog-top-transition {
  &-enter-active {
    transition-duration: 225ms;
    transition-timing-function: settings.$decelerated-easing;
  }

  &-leave-active {
    transition-duration: 125ms;
    transition-timing-function: settings.$accelerated-easing;
  }

  &-enter-active,
  &-leave-active {
    transition-property: transform, opacity;
    pointer-events: none;
  }
}

.dialog-transition {
  &-enter-from, &-leave-to {
    transform: scale(0.9);
    opacity: 0;
  }

  &-enter-to, &-leave-from {
    opacity: 1;
  }
}

.dialog-bottom-transition {
  &-enter-from, &-leave-to {
    transform: translateY(calc(50vh + 50%));
  }
}

.dialog-top-transition {
  &-enter-from, &-leave-to {
    transform: translateY(calc(-50vh - 50%));
  }
}

.picker-transition,
.picker-reverse-transition {
  @include transition-default();

  &-enter-from,
  &-leave-to {
    opacity: 0;
  }

  &-leave-from,
  &-leave-active,
  &-leave-to {
    position: absolute;
  }

  &-enter-active,
  &-leave-active {
    transition-property: transform, opacity;
  }
}

.picker-transition {
  @include transition-default();

  &-enter-from {
    transform: translate(0, 100%);
  }

  &-leave-to {
    transform: translate(0, -100%);
  }
}

.picker-reverse-transition {
  @include transition-default();

  &-enter-from {
    transform: translate(0, -100%);
  }

  &-leave-to {
    transform: translate(0, 100%);
  }
}

// Generic transitions
.expand-transition {
  @include transition-default();

  &-enter-active,
  &-leave-active {
    transition-property: height;
  }
}

.expand-x-transition {
  @include transition-default();

  &-enter-active,
  &-leave-active {
    transition-property: width;
  }
}

.scale-transition {
  @include transition-default();
  @include fade-out();

  &-enter-from {
    opacity: 0;
    transform: scale(0);
  }

  &-enter-active,
  &-leave-active {
    transition-property: transform, opacity;
  }
}

.scale-rotate-transition {
  @include transition-default();
  @include fade-out();

  &-enter-from {
    opacity: 0;
    transform: scale(0) rotate(-45deg);
  }

  &-enter-active,
  &-leave-active {
    transition-property: transform, opacity;
  }
}

.scale-rotate-reverse-transition {
  @include transition-default();
  @include fade-out();

  &-enter-from {
    opacity: 0;
    transform: scale(0) rotate(45deg);
  }

  &-enter-active,
  &-leave-active {
    transition-property: transform, opacity;
  }
}

.message-transition {
  @include transition-default();

  &-enter-from, &-leave-to {
    opacity: 0;
    transform: translateY(-15px);
  }

  &-leave-from, &-leave-active {
    position: absolute;
  }

  &-enter-active,
  &-leave-active {
    transition-property: transform, opacity;
  }
}

.slide-y-transition {
  @include transition-default();

  &-enter-from, &-leave-to {
    opacity: 0;
    transform: translateY(-15px);
  }

  &-enter-active,
  &-leave-active {
    transition-property: transform, opacity;
  }
}

.slide-y-reverse-transition {
  @include transition-default();

  &-enter-from, &-leave-to {
    opacity: 0;
    transform: translateY(15px);
  }

  &-enter-active,
  &-leave-active {
    transition-property: transform, opacity;
  }
}

.scroll-y-transition {
  @include transition-default();

  &-enter-from, &-leave-to {
    opacity: 0;
  }

  &-enter-from {
    transform: translateY(-15px);
  }

  &-leave-to {
    transform: translateY(15px);
  }

  &-enter-active,
  &-leave-active {
    transition-property: transform, opacity;
  }
}

.scroll-y-reverse-transition {
  @include transition-default();

  &-enter-from, &-leave-to {
    opacity: 0;
  }

  &-enter-from {
    transform: translateY(15px);
  }

  &-leave-to {
    transform: translateY(-15px);
  }

  &-enter-active,
  &-leave-active {
    transition-property: transform, opacity;
  }
}

.scroll-x-transition {
  @include transition-default();

  &-enter-from, &-leave-to {
    opacity: 0;
  }

  &-enter-from {
    transform: translateX(-15px);
  }

  &-leave-to {
    transform: translateX(15px);
  }

  &-enter-active,
  &-leave-active {
    transition-property: transform, opacity;
  }
}

.scroll-x-reverse-transition {
  @include transition-default();

  &-enter-from, &-leave-to {
    opacity: 0;
  }

  &-enter-from {
    transform: translateX(15px);
  }

  &-leave-to {
    transform: translateX(-15px);
  }

  &-enter-active,
  &-leave-active {
    transition-property: transform, opacity;
  }
}

.slide-x-transition {
  @include transition-default();

  &-enter-from, &-leave-to {
    opacity: 0;
    transform: translateX(-15px);
  }

  &-enter-active,
  &-leave-active {
    transition-property: transform, opacity;
  }
}

.slide-x-reverse-transition {
  @include transition-default();

  &-enter-from, &-leave-to {
    opacity: 0;
    transform: translateX(15px);
  }

  &-enter-active,
  &-leave-active {
    transition-property: transform, opacity;
  }
}

.fade-transition {
  @include transition-default();

  &-enter-from, &-leave-to {
    opacity: 0;
  }

  &-enter-active,
  &-leave-active {
    transition-property: opacity;
  }
}

.fab-transition {
  @include transition-default();

  &-enter-from, &-leave-to {
    transform: scale(0) rotate(-45deg);
  }

  &-enter-active,
  &-leave-active {
    transition-property: transform;
  }
}
